all: up
.PHONY: all up stop install install-php install-front install-admin install-admin-new build-front build-admin build-admin-new migrate migrations-reset xdebug-on xdebug-off clean-cache start

ROOT_DIR := $(strip $(shell dirname "$(realpath $(firstword $(MAKEFILE_LIST)))"))

start:
	sh ./docker/db/make-dump.sh
	make up
	make install
	make build
	make clean-cache

up:
	docker compose up -d

stop:
	docker compose stop

install: install-php install-front install-admin install-admin-new

install-php: up
	docker compose run --rm -it app php composer.phar install

install-front: up
	docker compose run --rm -it front npm install --yes

install-admin: src/admin/package.json src/admin/package-lock.json
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:14 npm install

install-admin-new: up
	docker compose run --rm -it admin npm install --yes
	docker compose run --rm -it admin npm rebuild node-sass

build: build-front build-admin build-admin-new

build-front: install-front
	docker compose run --rm -it front npm run build

build-admin: install-admin
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:14 npx gulp

build-admin-new: install-admin-new
	docker compose run --rm -it admin npm run build

migrate: install-php
	docker compose run --rm -it app php bin/console migrations:continue

migrations-reset: install-php
	docker compose run --rm -it app php bin/console migrations:reset

populate-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:create -psc

xdebug-on:
	SUPERADMIN_XDEBUG=on docker compose up --force-recreate --no-deps -d app

xdebug-off:
	SUPERADMIN_XDEBUG=off docker compose up --force-recreate --no-deps -d app

rebuild:
	docker compose up --build

orbstack:
	sh ./docker/orbstack.sh
	make start

docker-start:
	sh ./docker/docker.sh
	make start

diff:
	sh ./docker/db/make-dump.sh
	sh ./docker/db/compare-db.sh

drop-db:
	sh ./docker/db/drop-database.sh

phpstan:
	docker compose run --rm -it app php vendor/bin/phpstan analyse -c .phpstan.neon --memory-limit=2G

clear-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:purge -f

redis-clear-cache: install-php
	docker compose exec redis sh -c "redis-cli -n 0 FLUSHDB"

redis-clear-front: install-php
	docker compose exec redis sh -c "redis-cli -n 2 FLUSHDB"

messenger-consume:
	docker compose exec app sh -c "MESSENGER_CONSUMER_NAME=01 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=02 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=03 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=04 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=05 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=06 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=07 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=08 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=09 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500 & MESSENGER_CONSUMER_NAME=10 php bin/console messenger:consume elasticPriorityFront elasticFront -l 500"

composer-audit:
	docker compose run --rm -it app php composer.phar audit --locked

clean-cache:
	$(MAKE) redis-clear-storage
	$(ROOT_DIR)/cleanCache.sh
