[program:elastic]
command=php /var/www/html/bin/console messenger:consume elasticPriorityFront elasticFront --time-limit=30 --limit=20
user=root
numprocs=3
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d,LOCAL_DOCKER=1

[program:erp]
command=php -d memory_limit=4G /var/www/html/bin/console messenger:consume erpFront --limit=5 --time-limit=180
user=root
numprocs=1
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d,LOCAL_DOCKER=1

[program:default]
command=php -d memory_limit=4G /var/www/html/bin/console messenger:consume defaultFront --limit=10 --time-limit=180
user=root
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d,LOCAL_DOCKER=1
