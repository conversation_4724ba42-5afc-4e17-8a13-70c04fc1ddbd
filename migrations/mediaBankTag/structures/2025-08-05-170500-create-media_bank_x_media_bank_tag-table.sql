DROP TABLE IF EXISTS `media_bank_x_media_bank_tag`;

CREATE TABLE IF NOT EXISTS `media_bank_x_media_bank_tag` (
   `mediaBankId` INT(11) NOT NULL,
   `mediaBankTagId` INT(11) NOT NULL,
   PRIMARY KEY (`mediaBankId`, `mediaBankTagId`) USING BTREE,
   INDEX `FK_media_bank_x_media_bank_tag_media_bank` (`mediaBankId`) USING BTREE,
   INDEX `FK_media_bank_x_media_bank_tag_media_bank_tag` (`mediaBankTagId`) USING BTREE,
   CONSTRAINT `FK_media_bank_x_media_bank_tag_media_bank_tag` FOREIGN KEY (`mediaBankTagId`) REFERENCES `media_bank_tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_media_bank_x_media_bank_tag_media_bank` FOREIGN KEY (`mediaBankId`) REFERENCES `media_bank` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;
