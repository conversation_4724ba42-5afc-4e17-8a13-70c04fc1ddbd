DROP TABLE IF EXISTS `media_bank_tag_localization`;

CREATE TABLE IF NOT EXISTS `media_bank_tag_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`mediaBankTagId` INT(11) NOT NULL DEFAULT '1',
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`public` INT(11) DEFAULT NULL,
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`sort` INT(11) DEFAULT NULL,
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_mediaBankTagId` (`mutationId`,`mediaBankTagId`),
	KEY `FK_media_bank_tag_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_media_bank_tag_localization_media_bank_tag` (`mediaBankTagId`) USING BTREE,
	CONSTRAINT `FK_media_bank_tag_localization_media_bank_tag` FOREIGN KEY (`mediaBankTagId`) REFERENCES `media_bank_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_media_bank_tag_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE='utf8mb4_unicode_520_ci';
