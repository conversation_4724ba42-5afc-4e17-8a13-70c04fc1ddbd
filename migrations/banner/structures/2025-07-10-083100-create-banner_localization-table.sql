CREATE TABLE IF NOT EXISTS `banner_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`bannerId` INT(11) NOT NULL DEFAULT '1',
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`public` INT(11) DEFAULT NULL,
	`publicFrom` DATETIME NULL DEFAULT NULL,
	`publicTo` DATETIME NULL DEFAULT NULL,
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_bannerId` (`mutationId`,`bannerId`),
	KEY `FK_banner_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_banner_localization_banner` (`bannerId`) USING BTREE,
	CONSTRAINT `FK_banner_localization_banner` FOREIGN KEY (`bannerId`) REFERENCES `banner` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_banner_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE='utf8mb4_unicode_520_ci';
