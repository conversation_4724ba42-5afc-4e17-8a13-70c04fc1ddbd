CREATE TABLE IF NOT EXISTS `client_testimonial_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`clientTestimonialId` INT(11) NOT NULL DEFAULT '1',
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_520_ci,
	`sort` INT(11) NOT NULL DEFAULT 0,
	`public` INT(11) DEFAULT NULL,
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE utf8mb4_unicode_520_ci,
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_clientTestimonialId` (`mutationId`,`clientTestimonialId`),
	<PERSON><PERSON>Y `FK_client_testimonial_localization_mutation` (`mutationId`) USING BTREE,
	<PERSON><PERSON>Y `FK_client_testimonial_localization_client_testimonial` (`clientTestimonialId`) USING BTREE,
	CONSTRAINT `FK_client_testimonial_localization_client_testimonial` FOREIGN KEY (`clientTestimonialId`) REFERENCES `client_testimonial` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_client_testimonial_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
