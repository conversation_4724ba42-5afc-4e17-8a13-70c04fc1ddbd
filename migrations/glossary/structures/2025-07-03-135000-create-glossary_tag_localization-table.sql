CREATE TABLE IF NOT EXISTS `glossary_tag_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`glossaryTagId` INT(11) DEFAULT NULL,
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`alphabetGroup` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`nameAnchor` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`nameTitle` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`description` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`keywords` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`title` VARCHAR(255) COLLATE 'utf8mb4_unicode_520_ci' DEFAULT NULL,
	`public` INT(11) DEFAULT NULL,
	`forceNoIndex` INT(11) DEFAULT '0',
	`hideInSearch` INT(11) DEFAULT '0',
	`hideInSitemap` INT(11) DEFAULT '0',
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_glossaryTagId` (`mutationId`,`glossaryTagId`),
	KEY `FK_glossary_tag_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_glossary_tag_localization_glossary_tag` (`glossaryTagId`) USING BTREE,
	CONSTRAINT `FK_glossary_tag_localization_glossary_tag` FOREIGN KEY (`glossaryTagId`) REFERENCES `glossary_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_glossary_tag_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE='utf8mb4_unicode_520_ci';
