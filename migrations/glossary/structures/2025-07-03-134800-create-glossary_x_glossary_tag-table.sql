CREATE TABLE IF NOT EXISTS `glossary_x_glossary_tag` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`glossaryId` INT(11) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`glossaryTagId` INT(11) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_glossary_x_glossary_tag_glossary` (`glossaryId`) USING BTREE,
	INDEX `FK_glossary_x_glossary_tag_glossary_tag` (`glossaryTagId`) USING BTREE,
	CONSTRAINT `FK_glossary_x_glossary_tag_glossary_tag` FOREIGN KEY (`glossaryTagId`) REFERENCES `glossary_tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_glossary_x_glossary_tag_glossary` FOREIGN KEY (`glossaryId`) REFERENCES `glossary` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;