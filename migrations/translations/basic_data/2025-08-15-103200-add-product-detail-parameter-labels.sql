DELETE FROM `string` WHERE `name` IN ('product_kod', 'ean', 'isbn', 'p_n');

INSERT INTO `string` (`lg`, `name`, `value`, `usedAt`)
VALUES
	('sk', 'product_kod', 'Produkt kód', NULL),
	('cs', 'product_kod', 'Produkt kód', NULL),
	('sk', 'ean', 'EAN', NULL),
	('cs', 'ean', 'EAN', NULL),
	('sk', 'isbn', 'IBSN', NULL),
	('cs', 'isbn', 'IBSN', NULL),
    ('sk', 'p_n', 'P/N.', NULL),
    ('cs', 'p_n', 'P/N.', NULL);
	
	


