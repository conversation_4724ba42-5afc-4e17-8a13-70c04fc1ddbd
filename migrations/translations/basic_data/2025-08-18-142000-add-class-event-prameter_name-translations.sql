DELETE FROM `string` WHERE `name` IN ('class_event_date_from', 'class_event_date_to', 'class_event_time_from', 'class_event_time_to', 'class_event_city', 'class_event_place');

INSERT INTO `string` (`lg`, `name`, `value`, `usedAt`)
VALUES
	('sk', 'class_event_date_from', 'Dátum od', NULL),
	('cs', 'class_event_date_from', 'Dátum od', NULL),
	('sk', 'class_event_date_to', 'Dátum do', NULL),
	('cs', 'class_event_date_to', 'Dátum do', NULL),
	('sk', 'class_event_time_from', 'Čas od', NULL),
	('cs', 'class_event_time_from', 'Čas od', NULL),
	('sk', 'class_event_time_to', 'Čas do', NULL),
	('cs', 'class_event_time_to', 'Čas do', NULL),
	('sk', 'class_event_city', 'Město', NULL),
	('cs', 'class_event_city', 'Město', NULL),
	('sk', 'class_event_place', 'Místo', NULL),
	('cs', 'class_event_place', 'Místo', NULL);
	
	


