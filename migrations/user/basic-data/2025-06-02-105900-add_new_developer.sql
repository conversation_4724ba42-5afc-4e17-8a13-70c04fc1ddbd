INSERT INTO `user` 
	(`extId`, `email`, `password`, `role`, `firstname`, `lastname`, `phone`, `street`, `city`, `zip`, `stateId`, `company`, `ic`, `dic`, `created`, `createdTime`, `edited`, `editedTime`, `lastLogin`, `customAddress<PERSON>son`, `orderCount`, `priceLevelId`, `customFieldsJson`, `googleId`, `facebookId`, `freeTransit`, `payWithInvoice`, `preferredCurrency`, `isClubMember`, `seznamId`, `isActive`, `activatedTime`)
VALUES
	(NULL, '<EMAIL>', '$2y$12$cQ1zX6f7rD/4yxoQtFXHreX41hsGpUTwuTF7QKyEw19AzbelBe8Ee', 'developer', '', '', '', '', '', '', NULL, '', '', '', NULL, '2025-05-22 16:32:20', NULL, NULL, NULL, NULL, '0', '1', '{}', NULL, NULL, '0', '0', NULL, '0', NULL, '1', NULL)
ON DUPLICATE KEY UPDATE email = email;

SET @romanId = LAST_INSERT_ID();

INSERT INTO `user_mutation` (`userId`, `mutationId`, `newsletter`) VALUES (@romanId, 1, 0);