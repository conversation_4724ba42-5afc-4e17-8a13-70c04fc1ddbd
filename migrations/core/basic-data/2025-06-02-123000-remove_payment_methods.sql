DELETE FROM `payment_method_currency` WHERE paymentMethodId IN (
    SELECT id FROM payment_method WHERE paymentMethodUniqueIdentifier IN (
        'BenefitCard', 'PluxeeCard', 'EdenredCard', 'Certificate', 'InvoicePayment', 'Legacy'
    )
);

DELETE FROM `payment_method_price` WHERE paymentMethodId IN (
    SELECT id FROM payment_method WHERE paymentMethodUniqueIdentifier IN (
        'BenefitCard', 'PluxeeCard', 'EdenredCard', 'Certificate', 'InvoicePayment', 'Legacy'
    )
);

DELETE FROM `delivery_method_x_payment_method` WHERE paymentMethodId IN (
    SELECT id FROM payment_method WHERE paymentMethodUniqueIdentifier IN (
        'BenefitCard', 'PluxeeCard', 'EdenredCard', 'Certificate', 'InvoicePayment', 'Legacy'
    )
);

DELETE FROM payment_method WHERE paymentMethodUniqueIdentifier IN (
    'BenefitCard', 'PluxeeCard', 'EdenredCard', 'Certificate', 'InvoicePayment', 'Legacy'
);

