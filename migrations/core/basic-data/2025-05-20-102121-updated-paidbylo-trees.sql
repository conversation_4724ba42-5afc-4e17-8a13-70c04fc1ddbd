SET NAMES utf8mb4;

INSERT INTO `tree` (
	`id`, `mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`
) VALUES
	  (2932, 1, 1, NULL, 139, 398, 2, '1|398|', 12, 1, 'paidByLo', 0, '2025-04-23 17:31:33', '2025-04-23 17:31:00', 32, '2025-05-06 10:25:16', ':Front:PaidByLo:default', 'common', '2025-04-23 17:31:00', '2125-04-23 17:31:00', 'Kurzy hrazené UP', 'Kurzy hrazené UP', '', 'Kurzy hrazené UP', '', '', '', '', 1, 0, 0, 0, 0, 0, 'Výborně, vybrali jste si kurz hrazený Úřadem práce. Nejprve pár základních údajů o Vás.', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}', '{}', NULL, NULL),
	  (2933, 1, 1, NULL, 140, 398, 2, '1|398|', 13, 1, 'paidByLoQuestionare', 0, '2025-04-23 17:31:55', '2025-04-23 17:31:00', 32, '2025-05-06 11:48:26', ':Front:PaidByLo:questionare', 'common', '2025-04-23 17:31:00', '2125-04-23 17:31:00', 'Kurzy hrazené UP - dotazník', 'Kurzy hrazené UP - dotazník', '', 'Kurzy hrazené UP - dotazník', '', 'Dotazník pro ÚP ČR k přihlášení na', '', '', 1, 0, 0, 0, 0, 0, 'Nezabere vám to více než pět minut, můžete být struční. Všechna pole formuláře je ale nutné vyplnit.', '', NULL, NULL, NULL, NULL, NULL, NULL, '{\"base\":[{\"show_eu_bnr\":true}],\"header\":[{\"extra\":[{\"type\":\"none\"}]}]}', '{}', NULL, NULL)
ON DUPLICATE KEY UPDATE
					 `mutationId` = VALUES(`mutationId`),
					 `rootId` = VALUES(`rootId`),
					 `extId` = VALUES(`extId`),
					 `treeParentId` = VALUES(`treeParentId`),
					 `parentId` = VALUES(`parentId`),
					 `level` = VALUES(`level`),
					 `path` = VALUES(`path`),
					 `sort` = VALUES(`sort`),
					 `last` = VALUES(`last`),
					 `uid` = VALUES(`uid`),
					 `created` = VALUES(`created`),
					 `createdTime` = VALUES(`createdTime`),
					 `createdTimeOrder` = VALUES(`createdTimeOrder`),
					 `edited` = VALUES(`edited`),
					 `editedTime` = VALUES(`editedTime`),
					 `template` = VALUES(`template`),
					 `type` = VALUES(`type`),
					 `publicFrom` = VALUES(`publicFrom`),
					 `publicTo` = VALUES(`publicTo`),
					 `name` = VALUES(`name`),
					 `nameAnchor` = VALUES(`nameAnchor`),
					 `nameAnchorBreadcrumb` = VALUES(`nameAnchorBreadcrumb`),
					 `nameTitle` = VALUES(`nameTitle`),
					 `nameShort` = VALUES(`nameShort`),
					 `nameHeading` = VALUES(`nameHeading`),
					 `description` = VALUES(`description`),
					 `keywords` = VALUES(`keywords`),
					 `public` = VALUES(`public`),
					 `score` = VALUES(`score`),
					 `forceNoIndex` = VALUES(`forceNoIndex`),
					 `hideInSearch` = VALUES(`hideInSearch`),
					 `hideInSitemap` = VALUES(`hideInSitemap`),
					 `hideInMenu` = VALUES(`hideInMenu`),
					 `annotation` = VALUES(`annotation`),
					 `content` = VALUES(`content`),
					 `hideFirstImage` = VALUES(`hideFirstImage`),
					 `links` = VALUES(`links`),
					 `seoTitleFilter` = VALUES(`seoTitleFilter`),
					 `seoAnnotationFilter` = VALUES(`seoAnnotationFilter`),
					 `seoDescriptionFilter` = VALUES(`seoDescriptionFilter`),
					 `videos` = VALUES(`videos`),
					 `customFieldsJson` = VALUES(`customFieldsJson`),
					 `customContentJson` = VALUES(`customContentJson`),
					 `productAttachedId` = VALUES(`productAttachedId`),
					 `hasLinkedCategories` = VALUES(`hasLinkedCategories`);
