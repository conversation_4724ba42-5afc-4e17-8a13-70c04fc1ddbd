-- Adminer 5.3.0 MariaDB 10.11.11-MariaDB-ubu2204 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `youtube_video`;
CREATE TABLE `youtube_video` (
								 `id` int(11) NOT NULL AUTO_INCREMENT,
								 `importedAt` datetime NOT NULL DEFAULT current_timestamp(),
								 `videoId` varchar(100) NOT NULL,
								 `isShort` tinyint(1) NOT NULL DEFAULT 0,
								 `embeddable` tinyint(1) NOT NULL DEFAULT 0,
								 `publishedAt` datetime NOT NULL,
								 `durationInSeconds` int(11) NOT NULL,
								 `title` varchar(255) NOT NULL,
								 `description` text NOT NULL,
								 `embedUrl` text NOT NULL,
								 `videoUrl` text NOT NULL,
								 `metadata` text NOT NULL DEFAULT '{}',
								 `stats` text NOT NULL DEFAULT '{}',
								 PRIMAR<PERSON> KEY (`id`),
								 UNIQUE KEY `videoId` (`videoId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2025-08-28 06:44:06 UTC
