-- Adminer 5.3.0 MariaDB 10.11.11-MariaDB-ubu2204 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand` (
						 `id` int(11) NOT NULL AUTO_INCREMENT,
						 `extId` varchar(255) DEFAULT NULL,
						 `internalName` varchar(255) NOT NULL,
						 `parameterValueBrandId` int(11) DEFAULT NULL,
						 `customFieldsJson` text DEFAULT NULL,
						 PRIMARY KEY (`id`),
						 KEY `parameterValueId` (`parameterValueBrandId`),
						 CONSTRAINT `brand_ibfk_1` FOREIGN KEY (`parameterValueBrandId`) REFERENCES `parameter_value` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


DROP TABLE IF EXISTS `brand_localization`;
CREATE TABLE `brand_localization` (
									  `id` int(11) NOT NULL AUTO_INCREMENT,
									  `brandId` int(11) NOT NULL,
									  `mutationId` int(11) NOT NULL,
									  `name` varchar(255) NOT NULL,
									  `nameAnchor` varchar(255) DEFAULT NULL,
									  `nameTitle` varchar(255) DEFAULT NULL,
									  `description` text DEFAULT NULL,
									  `keywords` text DEFAULT NULL,
									  `title` varchar(255) DEFAULT NULL,
									  `public` tinyint(1) NOT NULL DEFAULT 0,
									  `publicFrom` datetime DEFAULT NULL,
									  `publicTo` datetime DEFAULT NULL,
									  `editedTime` datetime DEFAULT NULL,
									  `edited` int(11) DEFAULT NULL,
									  `customFieldsJson` text DEFAULT NULL,
									  `customContentJson` text DEFAULT NULL,
									  PRIMARY KEY (`id`),
									  KEY `brandId` (`brandId`),
									  KEY `mutationId` (`mutationId`),
									  CONSTRAINT `brand_localization_ibfk_1` FOREIGN KEY (`brandId`) REFERENCES `brand` (`id`) ON DELETE CASCADE,
									  CONSTRAINT `brand_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2025-08-18 18:42:12 UTC
