
delete from `tree` where uid = 'step3';
INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(1,	1,	452,	398,	2,	'1|398|',	7,	1,	'step3',	0,	'2024-03-14 09:30:15',	'2024-03-14 09:30:00',	33,	'2024-03-14 09:30:48',	':Front:Order:step3',	'common',	'2024-03-14 09:30:00',	'2124-03-14 09:30:00',	'Objednávka dokončena',	'Objednávka dokončena',	'Objednávka dokončena',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);


SET @step3Id = LAST_INSERT_ID();
delete from `alias` where alias = 'objednavka-dokoncena' and module = 'tree';
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('objednavka-dokoncena',	'tree',	@step3Id,	1);
