DELETE FROM `price_level`
WHERE ((`id` = '2') OR (`id` = '4') OR (`id` = '8') OR (`id` = '9')  OR (`id` = '6'));


UPDATE `price_level` SET `name` = 'Default cena' WHERE `id` = '1';
UPDATE `price_level` SET `name` = 'Akční cena' WHERE `id` = '3';


INSERT INTO `price_level` (`id`, `type`, `name`, `hasValid`, `discountPriceId`)
VALUES ('4', 'requalification', 'UP 82% cena', '0', NULL);


INSERT INTO `price_level` (`id`, `type`, `name`, `hasValid`, `discountPriceId`)
VALUES ('6', 'last_minute', 'LastMinute cena', '0', NULL);


