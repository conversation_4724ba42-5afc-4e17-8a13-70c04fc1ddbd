ALTER TABLE `price_level`
	ADD `isSelectable` tinyint(1) NOT NULL DEFAULT '0';
UPDATE `price_level` SET `isSelectable` = '1' WHERE `id` = '1';

INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('bronze', 'Bronze', '0', NULL, '1');

INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('bronze_discount', 'Bronze - akční cena', '1', NULL, '0');

SET @bronzeDiscountId = LAST_INSERT_ID();

UPDATE `price_level` SET `discountPriceId` = @bronzeDiscountId WHERE `type` = 'bronze';


INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('silver', 'Silver', '0', NULL, '1');

INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('silver_discount', 'Silver - akční cena', '1', NULL, '0');

SET @silverDiscountId = LAST_INSERT_ID();

UPDATE `price_level` SET `discountPriceId` = @silverDiscountId WHERE `type` = 'silver';

INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('gold', 'Gold', '0', NULL, '1');

INSERT INTO `price_level` (`type`, `name`, `hasValid`, `discountPriceId`, `isSelectable`)
VALUES ('gold_discount', 'Gold - akční cena', '1', NULL, '0');

SET @goldDiscountId = LAST_INSERT_ID();

UPDATE `price_level` SET `discountPriceId` = @goldDiscountId WHERE `type` = 'gold';
