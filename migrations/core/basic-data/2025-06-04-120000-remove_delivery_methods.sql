DELETE FROM `delivery_method_x_state` WHERE `deliveryMethodId` IN (
    SELECT `id` FROM `delivery_method` WHERE `deliveryMethodUniqueIdentifier` IN (
        'BalikovnaPickup', 'CzechPost', 'CzechPostPickup', 'Legacy', 'ZasilkovnaPickup', 'Zasilkovna', 'PPL', 'GLS'
    )
);

DELETE FROM `delivery_method_x_payment_method` WHERE `deliveryMethodId` IN (
    SELECT `id` FROM `delivery_method` WHERE `deliveryMethodUniqueIdentifier` IN (
        'BalikovnaPickup', 'CzechPost', 'CzechPostPickup', 'Legacy', 'ZasilkovnaPickup', 'Zasilkovna', 'PPL', 'GLS'
    )
);

DELETE FROM `delivery_method_price` WHERE `deliveryMethodId` IN (
    SELECT `id` FROM `delivery_method` WHERE `deliveryMethodUniqueIdentifier` IN (
        'BalikovnaPickup', 'CzechPost', 'CzechPostPickup', 'Legacy', '<PERSON><PERSON><PERSON>ovnaPickup', '<PERSON>asilkovna', 'PPL', 'GLS'
    )
);

DELETE FROM `delivery_method_currency` WHERE `deliveryMethodId` IN (
    SELECT `id` FROM `delivery_method` WHERE `deliveryMethodUniqueIdentifier` IN (
        'BalikovnaPickup', 'CzechPost', 'CzechPostPickup', 'Legacy', 'ZasilkovnaPickup', 'Zasilkovna', 'PPL', 'GLS'
    )
);

DELETE FROM `delivery_method` 
    WHERE `deliveryMethodUniqueIdentifier` IN (
        'BalikovnaPickup', 'CzechPost', 'CzechPostPickup', 'Legacy', 'ZasilkovnaPickup', 'Zasilkovna', 'PPL', 'GLS'
    );