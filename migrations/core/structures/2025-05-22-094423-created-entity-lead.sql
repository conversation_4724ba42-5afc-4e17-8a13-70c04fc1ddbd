-- Adminer 5.2.1 MariaDB 10.11.11-MariaDB-ubu2204 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `lead`;
CREATE TABLE `lead` (
						`id` int(11) NOT NULL AUTO_INCREMENT,
						`extId` varchar(100) DEFAULT NULL,
						`userId` int(11) DEFAULT NULL,
						`email` varchar(255) NOT NULL,
						`name` varchar(255) NOT NULL,
						`phone` varchar(255) NOT NULL,
						`leadName` varchar(255) NOT NULL,
						`leadType` varchar(255) NOT NULL,
						`data` text NOT NULL DEFAULT '{}',
						`createdTime` datetime NOT NULL,
						`lastSyncTime` datetime DEFAULT NULL,
						PRIMARY <PERSON>EY (`id`),
						<PERSON><PERSON><PERSON> `userId` (`userId`),
						CONSTRAINT `lead_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2025-05-22 07:43:54 UTC
