
CREATE TABLE IF NOT EXISTS `like_dislike` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`ip` VARCHAR(255) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_520_ci,
	`entityName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_520_ci,
	`entityId` int(11) NOT NULL,
	`likeDislike` ENUM('like', 'dislike') NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_ip_entity` (`ip`, `entityName`, `entityId`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
