INSERT INTO `payment_method` 
    (`externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`)
VALUES
	(NULL, 'Card', 'Apple Pay', '', NULL, NULL, '3', '1', '1', '1', '1', '{\"1\":\"standard\"}', '{}');

SET @applePayId = LAST_INSERT_ID();

INSERT INTO `payment_method_currency` 
    (`paymentMethodId`, `currency`)
VALUES
	(@applePayId, 'CZK');

INSERT INTO `payment_method_price` 
    (`paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`)
VALUES
	(@applePayId, '1', '1', '0.0000', 'CZK');

INSERT INTO `payment_method` 
    (`externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`)
VALUES
	(NULL, 'Card', 'Google Pay', '', NULL, NULL, '4', '1', '1', '1', '1', '{\"1\":\"standard\"}', '{}');

SET @googlePayId = LAST_INSERT_ID();

INSERT INTO `payment_method_currency` 
    (`paymentMethodId`, `currency`)
VALUES
	(@googlePayId, 'CZK');

INSERT INTO `payment_method_price` 
    (`paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`)
VALUES
	(@googlePayId, '1', '1', '0.0000', 'CZK');



