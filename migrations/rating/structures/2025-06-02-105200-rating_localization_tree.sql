CREATE TABLE `rating_localization_x_tree` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`ratingLocalizationId` INT(11) NOT NULL,
	`treeId` INT(11) NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_rating_localization_x_tree_rating_localization` (`ratingLocalizationId`) USING BTREE,
	INDEX `FK_rating_localization_x_tree_tree` (`treeId`) USING BTREE,
	CONSTRAINT `FK_rating_localization_x_tree_rating_localization` FOREIGN KEY (`ratingLocalizationId`) REFERENCES `rating_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_rating_localization_x_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;