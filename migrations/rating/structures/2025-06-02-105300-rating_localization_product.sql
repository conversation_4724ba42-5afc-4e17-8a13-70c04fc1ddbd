CREATE TABLE `rating_localization_x_product` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`ratingLocalizationId` INT(11) NOT NULL,
	`productId` INT(11) NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_rating_localization_x_product_rating_localization` (`ratingLocalizationId`) USING BTREE,
	INDEX `FK_rating_localization_x_product_product` (`productId`) USING BTREE,
	CONSTRAINT `FK_rating_localization_x_product_rating_localization` FOREIGN KEY (`ratingLocalizationId`) REFERENCES `rating_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_rating_localization_x_product_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;