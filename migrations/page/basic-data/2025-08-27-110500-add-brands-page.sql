INSERT INTO `tree_parent` 
    (`id`)
VALUES
	(null);

SET @brandsTreeParentId = LAST_INSERT_ID();

INSERT INTO `tree` (`mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFields<PERSON>son`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`)
VALUES
	('1', '1', NULL, @brandsTreeParentId, '1', '1', '1|', '2', '1', 'brands', '0', '2025-08-21 13:42:23', '2025-08-21 13:42:00', '204', '2025-08-21 14:36:01', ':Brand:Front:Brand:default', 'common', '2025-08-21 13:42:00', '2125-08-21 13:42:00', 'Značky', 'Značky', '', 'Značky', '', '', '', '', '1', '0', '0', '0', '0', '0', 'Značky', '', NULL, NULL, NULL, NULL, NULL, NULL, '{\"base\":[{\"show_eu_bnr\":true}],\"virtual_category\":[{\"toggle\":\"systemHref\"}],\"catalog_header\":[{\"extra\":[{\"type\":\"none\"}]}]}', '{}', NULL, '0');

SET @brandsId = LAST_INSERT_ID();

INSERT INTO `alias` 
    (`alias`, `module`, `referenceId`, `mutationId`)
VALUES
    ('znacky', 'tree', @brandsId, '1');
