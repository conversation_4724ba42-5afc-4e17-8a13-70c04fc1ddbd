-- COMPARE PAGE
INSERT INTO `tree_parent` 
    (`id`)
VALUES
	(null);

SET @compareTreeParentId = LAST_INSERT_ID();

INSERT INTO `tree` 
    (`mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`)
VALUES
    ('1', '1', NULL, @compareTreeParentId, '43', '2', '1|43|', '10', '0', 'Compare', '0', '2025-05-29 15:29:21', '2025-05-29 15:29:00', '203', '2025-05-30 12:25:20', ':Front:Compare:default', 'common', '2025-05-29 15:29:00', '2125-05-29 15:29:00', 'Porovnávač', 'Porovnávač', '', 'Porovnávač', '', '', '', '', '1', '0', '0', '0', '0', '0', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}', '{}', NULL, NULL);

SET @compareId = LAST_INSERT_ID();

INSERT INTO `alias` 
    (`alias`, `module`, `referenceId`, `mutationId`)
VALUES
    ('compare', 'tree', @compareId, '1');

-- SUCCESSFULLY ADDED TO COMPARE PAGE
INSERT INTO `tree_parent` 
    (`id`)
VALUES
	(null);

SET @successfullyAddedToCompareTreeParentId = LAST_INSERT_ID();

INSERT INTO `tree` 
    (`mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`)
VALUES
    ('1', '1', NULL, @successfullyAddedToCompareTreeParentId, @compareId, '3', CONCAT('1|43|', @compareId, '|'), '1', '1', 'successfullyAddedToCompare', '0', '2025-05-29 11:25:17', '2025-05-29 11:25:00', '203', '2025-05-30 14:20:08', ':Front:Compare:successfullyAddedToCompare', 'common', '2025-05-29 11:25:00', '2125-05-29 11:25:00', 'Successfully Added To Compare', 'Nová stránka', '', 'Nová stránka', '', 'Porovnat produkty', '', '', '1', '0', '0', '0', '0', '0', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}', '{}', NULL, NULL);

SET @successfullyAddedToCompareId = LAST_INSERT_ID();

INSERT INTO `alias` 
    (`alias`, `module`, `referenceId`, `mutationId`)
VALUES
    ('successfully-added-to-compare', 'tree', @successfullyAddedToCompareId, '1');
