INSERT INTO `tree` 
    (`mutationId`, `rootId`, `extId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`)
VALUES
    ('1', '1', NULL, '43', '2', '1|43|', '9', '1', 'faqForm', '0', '2025-06-26 14:40:34', '2025-06-26 14:40:00', '203', '2025-06-26 14:43:08', ':Faq:Front:Faq:faqForm', 'common', '2025-06-26 14:40:00', '2125-06-26 14:40:00', 'FAQ Form', 'FAQ Form', '', 'FAQ Form', '', '', '', '', '1', '0', '0', '0', '0', '0', 'FAQ Form', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}', '{}', NULL, NULL);

SET @faqFormId = LAST_INSERT_ID();

INSERT INTO `alias` 
    (`alias`, `module`, `referenceId`, `mutationId`)
VALUES
    ('faq-form', 'tree', @faqFormId, '1');
