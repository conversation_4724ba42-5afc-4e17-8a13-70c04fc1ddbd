DROP TABLE IF EXISTS `blog_localization_x_product`;

CREATE TABLE `blog_localization_x_product` (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `blogLocalizationId` int(11) NOT NULL,
        `productId` int(11) NOT NULL,
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `FK_blog_localization_x_product_blog_localization` (`blogLocalizationId`) USING BTREE,
        INDEX `FK_blog_localization_x_product_product` (`productId`) USING BTREE,
        CONSTRAINT `FK_blog_localization_x_product_blog_localization` FOREIGN KEY (`blogLocalizationId`) REFERENCES `blog_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
        CONSTRAINT `FK_blog_localization_x_product_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
    ) 
    ENGINE=InnoDB 
    DEFAULT CHARSET=utf8mb4 
    COLLATE=utf8mb4_unicode_ci;