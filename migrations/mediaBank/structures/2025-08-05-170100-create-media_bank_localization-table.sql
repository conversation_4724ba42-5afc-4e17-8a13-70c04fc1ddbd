DROP TABLE IF EXISTS `media_bank_localization`;

CREATE TABLE IF NOT EXISTS `media_bank_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`mediaBankId` INT(11) NOT NULL DEFAULT '1',
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`public` INT(11) DEFAULT NULL,
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`sort` INT(11) DEFAULT NULL,
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_mediaBankId` (`mutationId`,`mediaBankId`),
	KEY `FK_media_bank_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_media_bank_localization_media_bank` (`mediaBankId`) USING BTREE,
	CONSTRAINT `FK_media_bank_localization_media_bank` FOREIGN KEY (`mediaBankId`) REFERENCES `media_bank` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_media_bank_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE='utf8mb4_unicode_520_ci';
