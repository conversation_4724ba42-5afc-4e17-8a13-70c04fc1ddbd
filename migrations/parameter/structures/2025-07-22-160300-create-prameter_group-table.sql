
CREATE TABLE IF NOT EXISTS `parameter_group` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`internalName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_520_ci,
	`sort` INT(11) NOT NULL DEFAULT '0',
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
