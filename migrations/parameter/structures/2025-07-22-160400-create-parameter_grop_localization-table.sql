CREATE TABLE IF NOT EXISTS `parameter_group_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL,
	`parameterGroupId` INT(11) NOT NULL DEFAULT '1',
	`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`public` INT(11) DEFAULT NULL,
	`edited` INT(11) DEFAULT NULL,
	`editedTime` DATETIME DEFAULT NULL,
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContent<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_parameterGroupId` (`mutationId`,`parameterGroupId`),
	<PERSON><PERSON>Y `FK_parameter_group_localization_mutation` (`mutationId`) USING BTREE,
	<PERSON><PERSON><PERSON> `FK_parameter_group_localization_parameter_group` (`parameterGroupId`) USING BTREE,
	CONSTRAINT `FK_parameter_group_localization_parameter_group` FOREIGN KEY (`parameterGroupId`) REFERENCES `parameter_group` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_parameter_group_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE='utf8mb4_unicode_520_ci';
