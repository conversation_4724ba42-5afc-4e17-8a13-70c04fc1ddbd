<?php

declare(strict_types=1);

namespace App\Model\CustomField\Mapping;

use \stdClass;


readonly class CustomFieldData
{

	public function __construct(
		private \stdClass $customFieldJson,
		private CustomFieldStructureAnalyzer $analyzer,
	) {
	}

	public function getAnalyzer(): CustomFieldStructureAnalyzer
	{
		return $this->analyzer;
	}

	public function createDataMapper(string $fieldName): CustomFieldDataMapper
	{
		return new CustomFieldDataMapper($fieldName, $this->analyzer->getFieldConfig($fieldName), $this);
	}

	public function addToField(
		string $fieldName,
		array|stdClass $itemData,
		string $sectionKey = '0',
		string|int|null $itemKey = null
	): string|int {
		return $this->analyzer->getManager()->addToField($this->customFieldJson, $fieldName, $itemData, $sectionKey, $itemKey);
	}

	public function addDirectItem(string $fieldName, array|stdClass $itemData, string|int|null $itemKey = null): string|int
	{
		return $this->addToField($fieldName, $itemData, '0', $itemKey);
	}

	public function addSubItem(
		string $fieldName,
		array|stdClass $itemData,
		string $sectionKey = '0',
		string|int|null $itemKey = null
	): string|int {
		return $this->addToField($fieldName, $itemData, $sectionKey, $itemKey);
	}

	public function modifyProperties(string $fieldName, array|stdClass $properties, string $sectionKey = '0'): string
	{
		$result = $this->addToField($fieldName, $properties, $sectionKey);
		return is_string($result) ? $result : (string)$result;
	}

	public function getCustomFieldJson(): stdClass
	{
		return $this->customFieldJson;
	}

	public function toJson(bool $prettyPrint = false): string
	{
		$flags = $prettyPrint ? JSON_PRETTY_PRINT : 0;
		$result = json_encode($this->customFieldJson, $flags);

		if ($result === false) {
			throw new CustomFieldMapperException("Failed to encode CustomFields to JSON");
		}

		return $result;
	}
}
